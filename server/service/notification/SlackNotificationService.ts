/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import Initiative, { InitiativePlain } from '../../models/initiative';
import { MaterialityAssessmentUtrCodes } from '../../routes/validation-schemas/materiality-assessment';
import { customDateFormat } from '../../util/date';
import { assessmentTypeLabelMap } from '../materiality-assessment/constants';
import { getAddressText } from '../../util/initiative';
import { getCountryByCode } from '../location/CountryService';
import { getSlackProvider, SlackNotificationCategory, SlackProvider } from './delivery/Slack';
import { CreateAssessmentParams } from '../materiality-assessment/types';
import ValueList from '../../models/valueList';
import config from '../../config';

export class SlackNotificationService {
  constructor(private slackProvider: SlackProvider) {}

  private async getContextValueLists(context: CreateAssessmentParams['context']) {
    const valueListCodes = Object.keys(context);
    const valueLists = await ValueList.find({ code: { $in: valueListCodes } })
      .lean()
      .exec();
    return new Map(valueLists.map((v) => [v.code, v.options]));
  }

  private async getOnboardingMaterialityTrackerContent(
    params: Pick<CreateAssessmentParams, 'user' | 'context' | 'metadata'> & { initiative: InitiativePlain }
  ): Promise<string> {
    const { user, context, metadata, initiative } = params;

    const valueListsMap = await this.getContextValueLists(context);
    const getContextValue = (valueListCode: string, optionCode: string) => {
      const valueListOptions = valueListsMap.get(valueListCode);
      const option = valueListOptions?.find((o) => o.code === optionCode);
      return option?.name ?? optionCode;
    };

    return `
      *New Materiality Tracker Onboarding*

      A new client has completed the Materiality Tracker onboarding process.

      *User Details:*
      - *Name:* ${user.firstName} ${user.surname}
      - *Email:* ${user.email}

      *Company Details:*
      - *Name:* ${initiative.name}
      - *Location:* ${initiative.country ? getCountryByCode(initiative.country)?.name ?? '-' : '-'}
      - *Address:* ${getAddressText(initiative.address)}

      *Assessment Details:*
      - *Date:* ${customDateFormat(metadata.effectiveDate)}
      - *Type:* ${assessmentTypeLabelMap[metadata.assessmentType]}
      - *Industry:* ${getContextValue(
        MaterialityAssessmentUtrCodes.Sector,
        context[MaterialityAssessmentUtrCodes.Sector]
      )}
      - *Operating time:* ${getContextValue(
        MaterialityAssessmentUtrCodes.OperationTime,
        context[MaterialityAssessmentUtrCodes.OperationTime]
      )}
      - *Number of staff:* ${getContextValue(
        MaterialityAssessmentUtrCodes.NumStaff,
        context[MaterialityAssessmentUtrCodes.NumStaff]
      )}
      - *Annual sales:* ${getContextValue(
        MaterialityAssessmentUtrCodes.AnnualSales,
        context[MaterialityAssessmentUtrCodes.AnnualSales]
      )}
      - *Total liabilities:* ${getContextValue(
        MaterialityAssessmentUtrCodes.CapitalEmployed,
        context[MaterialityAssessmentUtrCodes.CapitalEmployed]
      )}
    `;
  }

  public async sendMaterialityOnboardingNotification(request: CreateAssessmentParams) {
    const { initiativeId, user, context, metadata } = request;

    const initiative = await Initiative.findById(initiativeId).lean().orFail().exec();
    const content = await this.getOnboardingMaterialityTrackerContent({ user, context, metadata, initiative });

    await this.slackProvider.create({
      title: 'New Materiality Tracker Onboarding',
      content: content,
      category: SlackNotificationCategory.Announcements,
      topic: `onboarding-${initiativeId}`,
      channel: config.notifications.slack.channels.signup,
    });
  }
}

let instance: SlackNotificationService;
export const getSlackNotificationService = () => {
  if (!instance) {
    instance = new SlackNotificationService(getSlackProvider());
  }
  return instance;
};
