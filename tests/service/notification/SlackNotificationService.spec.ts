import { expect } from 'chai';
import sinon from 'sinon';
import { createMongooseModel } from '../../setup';
import { SlackNotificationService } from '../../../server/service/notification/SlackNotificationService';
import Initiative from '../../../server/models/initiative';
import { MaterialityAssessmentUtrCodes } from '../../../server/routes/validation-schemas/materiality-assessment';
import { initiativeOneSimple, initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import { userOne } from '../../fixtures/userFixtures';
import {
  getSlackProvider,
  SlackNotificationCategory,
} from '../../../server/service/notification/delivery/Slack';
import { CreateAssessmentParams } from '../../../server/service/materiality-assessment/types';
import ValueList from '../../../server/models/valueList';

describe('SlackNotificationService', () => {
  const sandbox = sinon.createSandbox();
  const slackProvider = getSlackProvider();
  const slackNotificationService = new SlackNotificationService(slackProvider);

  const mockValueLists = [
    {
      code: MaterialityAssessmentUtrCodes.Sector,
      options: [{ code: '10203040', name: 'Industry Name' }],
    },
    {
      code: MaterialityAssessmentUtrCodes.OperationTime,
      options: [{ code: 'op-time-1', name: 'Operation Time Name' }],
    },
    {
      code: MaterialityAssessmentUtrCodes.NumStaff,
      options: [{ code: 'staff-1', name: 'Number of Staff Name' }],
    },
    {
      code: MaterialityAssessmentUtrCodes.AnnualSales,
      options: [{ code: 'sales-1', name: 'Annual Sales Name' }],
    },
    {
      code: MaterialityAssessmentUtrCodes.CapitalEmployed,
      options: [{ code: 'capital-1', name: 'Capital Employed Name' }],
    },
  ];

  afterEach(() => {
    sandbox.restore();
  });

  describe('sendMaterialityOnboardingNotification', () => {
    it('should create a notification with the correct content and parameters', async () => {
      const request: CreateAssessmentParams = {
        initiativeId: initiativeOneSimpleId,
        user: userOne,
        context: {
          [MaterialityAssessmentUtrCodes.Sector]: '10203040',
          [MaterialityAssessmentUtrCodes.OperationTime]: 'op-time-1',
          [MaterialityAssessmentUtrCodes.NumStaff]: 'staff-1',
          [MaterialityAssessmentUtrCodes.AnnualSales]: 'sales-1',
          [MaterialityAssessmentUtrCodes.CapitalEmployed]: 'capital-1',
        },
        metadata: {
          effectiveDate: new Date().toISOString(),
          assessmentType: 'type-1',
        },
        returnUrl: 'http://test.com',
      };

      const mockInitiative = {
        ...initiativeOneSimple,
        address: { line1: '123 Main St', city: 'Anytown', postcode: '12345' },
      };

      sandbox.stub(Initiative, 'findById').returns(createMongooseModel(mockInitiative));
      sandbox.stub(ValueList, 'find').returns(createMongooseModel(mockValueLists));
      const createNotificationStub = sandbox.stub(slackProvider, 'create');

      await slackNotificationService.sendMaterialityOnboardingNotification(request);

      expect(createNotificationStub.calledOnce).to.be.true;
      const [notificationData] = createNotificationStub.firstCall.args;

      expect(notificationData.title).to.equal('New Materiality Tracker Onboarding');
      expect(notificationData.category).to.equal(SlackNotificationCategory.Announcements);
      expect(notificationData.topic).to.equal(`onboarding-${request.initiativeId}`);
      expect(notificationData.content).to.contain('Industry Name');
      expect(notificationData.content).to.contain('Operation Time Name');
      expect(notificationData.content).to.contain('Number of Staff Name');
      expect(notificationData.content).to.contain('Annual Sales Name');
      expect(notificationData.content).to.contain('Capital Employed Name');
    });
  });
});
